import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Alert,
  StatusBar,
  Dimensions,
  Text,
} from 'react-native';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import Constants from 'expo-constants';
import VideoDownloadService from './services/VideoDownloadService';
import BackgroundDownloadService from './services/BackgroundDownloadService';
import DownloadTracker from './services/DownloadTracker';
import UrlValidator from './utils/UrlValidator';
import PermissionManager from './utils/PermissionManager';
import TabBrowser from './components/Browser/TabBrowser';
import BottomNavigation from './components/BottomNavigation';
import DownloadManager from './components/DownloadManager';
import BackgroundDownloadStatus from './components/BackgroundDownloadStatus';
import Settings from './components/Settings';
import VideoQualitySelector from './components/VideoQualitySelector';
import AudioSessionManager from './services/AudioSessionManager';

const { width, height } = Dimensions.get('window');

export default function App() {
  const [activeTab, setActiveTab] = useState('browser');
  const [showDownloadManager, setShowDownloadManager] = useState(false);
  const [showBackgroundDownloads, setShowBackgroundDownloads] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showQualitySelector, setShowQualitySelector] = useState(false);
  const [currentVideoUrl, setCurrentVideoUrl] = useState('');
  const [activeDownloadsCount, setActiveDownloadsCount] = useState(0);

  // Variáveis para compatibilidade (não usadas no novo design)
  const [url, setUrl] = useState('');
  const [urlValidation, setUrlValidation] = useState(null);

  useEffect(() => {
    // Inicializar serviços
    const initializeServices = async () => {
      VideoDownloadService.cleanupTempFiles();
      await BackgroundDownloadService.initialize();
      await DownloadTracker.initialize();

      // Initialize audio session for concurrent video playback
      try {
        await AudioSessionManager.initialize();
        console.log('[APP] Audio session initialized for concurrent playback');
      } catch (error) {
        console.error('[APP] Failed to initialize audio session:', error);
      }
    };

    initializeServices();

    // Monitorar downloads ativos
    const interval = setInterval(() => {
      const status = BackgroundDownloadService.getDownloadStatus();
      const activeCount = status.active.filter(d =>
        d.status === 'downloading' || d.status === 'queued'
      ).length;
      setActiveDownloadsCount(activeCount);
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const requestPermissions = async () => {
    const result = await PermissionManager.ensurePermissions();

    if (!result.success) {
      if (!result.cancelled) {
        Alert.alert('Erro', result.message);
      }
      return false;
    }

    return true;
  };

  const handleTabPress = (tabId) => {
    if (tabId === 'downloads') {
      // Se há downloads ativos, mostrar status em background
      if (activeDownloadsCount > 0) {
        setShowBackgroundDownloads(true);
      } else {
        setShowDownloadManager(true);
      }
    } else if (tabId === 'settings') {
      setShowSettings(true);
    } else {
      setActiveTab(tabId);
    }
  };

  const handleVideoDetected = (url) => {
    console.log('Vídeo detectado:', url);
    // Pode mostrar uma notificação ou indicador visual
  };

  const handleDownloadRequest = async (url, quality, metadata) => {
    if (!quality) {
      setCurrentVideoUrl(url);
      setShowQualitySelector(true);
      return;
    }

    try {
      const result = await VideoDownloadService.downloadVideo(url, (progress) => {});

      if (result) {
        await VideoDownloadService.saveToGallery(result.uri, result.fileName);

        await DownloadManager.addDownload({
          fileName: result.fileName,
          uri: result.uri,
          size: result.size,
          originalUrl: url,
          quality: quality,
          title: metadata?.title || 'Vídeo'
        });

        Alert.alert('Sucesso', 'Vídeo baixado com sucesso!');
      }
    } catch (error) {
      Alert.alert('Erro', 'Erro ao baixar vídeo');
    }
  };

  const handleQualitySelected = async (quality) => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      console.log('🚀 Iniciando download em background:', quality);

      // Verificar se temos uma URL real de vídeo
      const downloadUrl = quality.url;
      if (!downloadUrl || downloadUrl === currentVideoUrl) {
        throw new Error('URL de vídeo não encontrada. Tente selecionar outra qualidade.');
      }

      // Iniciar download em background
      const downloadId = await BackgroundDownloadService.startBackgroundDownload({
        url: downloadUrl,
        originalUrl: currentVideoUrl,
        quality: quality
      });

      // Fechar modal de qualidade
      setShowQualitySelector(false);

      console.log('✅ Download em background iniciado:', downloadId);

    } catch (error) {
      console.error('❌ Erro ao iniciar download:', error);

      let errorMessage = 'Não foi possível iniciar o download. ';

      if (error.message.includes('já está sendo baixado')) {
        errorMessage = 'Este vídeo já está sendo baixado em background. Verifique a aba Downloads.';
      } else if (error.message.includes('URL de vídeo não encontrada')) {
        errorMessage += 'Não foi possível extrair a URL real do vídeo. Tente outro vídeo ou qualidade.';
      } else {
        errorMessage += `Detalhes: ${error.message}\n\nTente:\n• Selecionar outra qualidade\n• Verificar conexão\n• Tentar outro vídeo`;
      }

      Alert.alert('❌ Erro no Download', errorMessage);
    }
  };

  return (
    <SafeAreaProvider>
      <SafeAreaView style={styles.container} edges={['top']}>
        <StatusBar
          barStyle="light-content"
          backgroundColor="#1a1a2e"
          translucent={false}
        />

        {/* Conteúdo principal baseado na aba ativa */}
        {activeTab === 'browser' && (
          <TabBrowser
            onVideoDetected={handleVideoDetected}
            onDownloadRequest={handleDownloadRequest}
          />
        )}

        {activeTab === 'bookmarks' && (
          <View style={styles.placeholderContainer}>
            <Text style={styles.placeholderText}>Favoritos em desenvolvimento</Text>
          </View>
        )}

        {/* Menu inferior de navegação */}
        <SafeAreaView edges={['bottom']} style={styles.bottomNavContainer}>
          <BottomNavigation
            activeTab={activeTab}
            onTabPress={handleTabPress}
            downloadCount={0} // TODO: implementar contagem real
            bookmarkCount={0} // TODO: implementar contagem real
            activeDownloadsCount={activeDownloadsCount}
          />
        </SafeAreaView>

        {/* Modais */}
        <DownloadManager
          visible={showDownloadManager}
          onClose={() => setShowDownloadManager(false)}
        />

        <BackgroundDownloadStatus
          visible={showBackgroundDownloads}
          onClose={() => setShowBackgroundDownloads(false)}
        />

        <Settings
          visible={showSettings}
          onClose={() => setShowSettings(false)}
        />

        <VideoQualitySelector
          visible={showQualitySelector}
          onClose={() => setShowQualitySelector(false)}
          videoUrl={currentVideoUrl}
          onQualitySelected={handleQualitySelected}
          onDownloadStart={handleQualitySelected}
        />


      </SafeAreaView>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  bottomNavContainer: {
    backgroundColor: '#1a1a2e',
  },
  placeholderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a2e',
  },
  placeholderText: {
    color: '#888',
    fontSize: 16,
  },
  downloadOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  downloadModal: {
    backgroundColor: '#1a1a2e',
    borderRadius: 15,
    padding: 30,
    width: width * 0.8,
    alignItems: 'center',
  },
  downloadTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  progressBarContainer: {
    width: '100%',
    height: 8,
    backgroundColor: '#2d2d44',
    borderRadius: 4,
    marginBottom: 15,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#6c5ce7',
    borderRadius: 4,
  },
  downloadStatus: {
    color: '#888',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 10,
  },
  downloadProgress: {
    color: '#6c5ce7',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
