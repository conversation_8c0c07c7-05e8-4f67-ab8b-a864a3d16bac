/**
 * Persistent Download Tracker
 * Maintains comprehensive download records across app sessions
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';

class DownloadTracker {
  static STORAGE_KEY = 'download_tracker_v2';
  static MAX_RECORDS = 1000;
  static CLEANUP_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours

  // Initialize tracker
  static async initialize() {
    try {
      await this.cleanupOldRecords();
      console.log('📊 DownloadTracker initialized');
    } catch (error) {
      console.error('❌ Failed to initialize DownloadTracker:', error);
    }
  }

  // Add download record
  static async addDownloadRecord(downloadInfo) {
    try {
      const records = await this.getRecords();
      
      const record = {
        id: this.generateRecordId(downloadInfo),
        originalUrl: downloadInfo.originalUrl,
        normalizedUrl: this.normalizeUrl(downloadInfo.originalUrl),
        quality: downloadInfo.quality?.quality || 'default',
        fileName: downloadInfo.fileName,
        fileUri: downloadInfo.uri || downloadInfo.fileUri,
        fileSize: downloadInfo.size || downloadInfo.fileSize,
        title: downloadInfo.title,
        timestamp: new Date().toISOString(),
        status: downloadInfo.status || 'completed',
        contentHash: await this.generateContentHash(downloadInfo),
        platform: downloadInfo.platform,
        duration: downloadInfo.duration
      };

      // Remove any existing record with same ID
      const filteredRecords = records.filter(r => r.id !== record.id);
      
      // Add new record at the beginning
      filteredRecords.unshift(record);
      
      // Keep only MAX_RECORDS
      const trimmedRecords = filteredRecords.slice(0, this.MAX_RECORDS);
      
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(trimmedRecords));
      
      console.log('📝 Download record added:', record.id);
      return record;
    } catch (error) {
      console.error('❌ Failed to add download record:', error);
      throw error;
    }
  }

  // Check if download exists
  static async checkDownloadExists(downloadInfo) {
    try {
      const records = await this.getRecords();
      const normalizedUrl = this.normalizeUrl(downloadInfo.originalUrl || downloadInfo.url);
      const quality = downloadInfo.quality?.quality || 'default';

      for (const record of records) {
        // Check by normalized URL and quality
        if (record.normalizedUrl === normalizedUrl && record.quality === quality) {
          // Verify file still exists
          if (record.fileUri) {
            try {
              const fileInfo = await FileSystem.getInfoAsync(record.fileUri);
              if (fileInfo.exists && fileInfo.size > 10000) {
                return {
                  exists: true,
                  record: record,
                  reason: 'url_quality_match'
                };
              }
            } catch (e) {
              // File doesn't exist, continue checking
            }
          }
        }

        // Check by content hash if available
        if (record.contentHash && downloadInfo.contentHash) {
          if (record.contentHash === downloadInfo.contentHash) {
            return {
              exists: true,
              record: record,
              reason: 'content_hash_match'
            };
          }
        }

        // Check by title similarity
        if (record.title && downloadInfo.title) {
          const similarity = this.calculateSimilarity(
            record.title.toLowerCase(),
            downloadInfo.title.toLowerCase()
          );
          if (similarity > 0.95 && record.quality === quality) {
            return {
              exists: true,
              record: record,
              reason: 'title_similarity'
            };
          }
        }
      }

      return { exists: false };
    } catch (error) {
      console.error('❌ Failed to check download exists:', error);
      return { exists: false };
    }
  }

  // Get all records
  static async getRecords() {
    try {
      const data = await AsyncStorage.getItem(this.STORAGE_KEY);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('❌ Failed to get records:', error);
      return [];
    }
  }

  // Clean up old records
  static async cleanupOldRecords() {
    try {
      const records = await this.getRecords();
      const now = new Date();
      const validRecords = [];

      for (const record of records) {
        const recordDate = new Date(record.timestamp);
        const ageInMs = now - recordDate;

        // Keep records from last 30 days or if file still exists
        if (ageInMs < 30 * 24 * 60 * 60 * 1000) {
          validRecords.push(record);
        } else if (record.fileUri) {
          try {
            const fileInfo = await FileSystem.getInfoAsync(record.fileUri);
            if (fileInfo.exists) {
              validRecords.push(record);
            }
          } catch (e) {
            // File doesn't exist, don't keep record
          }
        }
      }

      if (validRecords.length !== records.length) {
        await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(validRecords));
        console.log(`🧹 Cleaned up ${records.length - validRecords.length} old records`);
      }

      return validRecords;
    } catch (error) {
      console.error('❌ Failed to cleanup old records:', error);
      return [];
    }
  }

  // Generate record ID
  static generateRecordId(downloadInfo) {
    const url = downloadInfo.originalUrl || downloadInfo.url;
    const quality = downloadInfo.quality?.quality || 'default';
    const normalizedUrl = this.normalizeUrl(url);
    
    const idString = `${normalizedUrl}|${quality}`;
    return this.generateHash(idString);
  }

  // Generate content hash
  static async generateContentHash(downloadInfo) {
    try {
      const components = [
        downloadInfo.title,
        downloadInfo.duration,
        downloadInfo.size || downloadInfo.fileSize,
        downloadInfo.quality?.quality
      ].filter(Boolean);

      if (components.length === 0) return null;

      const hashInput = components.join('|');
      return this.generateHash(hashInput);
    } catch (error) {
      return null;
    }
  }

  // Normalize URL
  static normalizeUrl(url) {
    if (!url) return url;

    try {
      let cleanUrl = url.trim();
      if (!cleanUrl.startsWith('http')) {
        return cleanUrl;
      }

      const urlObj = new URL(cleanUrl);

      // Remove tracking parameters
      const paramsToRemove = [
        'utm_source', 'utm_medium', 'utm_campaign', 'utm_content', 'utm_term',
        'fbclid', 'gclid', 'msclkid', 'igshid', 'ref', 'ref_src', 'ref_url',
        't', 'si', 'feature', 'app', 'via', 'source', 'from'
      ];
      paramsToRemove.forEach(param => urlObj.searchParams.delete(param));

      // Normalize YouTube URLs
      if (urlObj.hostname.includes('youtube.com') || urlObj.hostname.includes('youtu.be')) {
        let videoId = null;

        if (urlObj.hostname.includes('youtu.be')) {
          videoId = urlObj.pathname.split('/')[1]?.split('?')[0];
        } else if (urlObj.searchParams.has('v')) {
          videoId = urlObj.searchParams.get('v');
        } else if (urlObj.pathname.includes('/embed/')) {
          videoId = urlObj.pathname.split('/embed/')[1]?.split('?')[0];
        }

        if (videoId && videoId.length === 11) {
          return `https://www.youtube.com/watch?v=${videoId}`;
        }
      }

      // Normalize Vimeo URLs
      if (urlObj.hostname.includes('vimeo.com')) {
        const pathParts = urlObj.pathname.split('/').filter(p => p);
        const videoId = pathParts.find(p => /^\d+$/.test(p));
        if (videoId) {
          return `https://vimeo.com/${videoId}`;
        }
      }

      return urlObj.toString();
    } catch (e) {
      return url;
    }
  }

  // Generate hash
  static generateHash(input) {
    if (!input) return 'empty';

    let hash = 0;
    const str = input.toString();

    if (str.length === 0) return 'empty';

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }

    return Math.abs(hash).toString(36).padStart(6, '0');
  }

  // Calculate string similarity
  static calculateSimilarity(str1, str2) {
    if (!str1 || !str2) return 0;
    
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  // Levenshtein distance
  static levenshteinDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  // Get statistics
  static async getStatistics() {
    try {
      const records = await this.getRecords();
      const now = new Date();
      
      const stats = {
        totalDownloads: records.length,
        last24Hours: 0,
        last7Days: 0,
        last30Days: 0,
        totalSize: 0,
        platforms: {},
        qualities: {}
      };

      records.forEach(record => {
        const recordDate = new Date(record.timestamp);
        const ageInMs = now - recordDate;
        const ageInHours = ageInMs / (1000 * 60 * 60);
        const ageInDays = ageInHours / 24;

        if (ageInHours <= 24) stats.last24Hours++;
        if (ageInDays <= 7) stats.last7Days++;
        if (ageInDays <= 30) stats.last30Days++;

        if (record.fileSize) stats.totalSize += record.fileSize;
        
        if (record.platform) {
          stats.platforms[record.platform] = (stats.platforms[record.platform] || 0) + 1;
        }
        
        if (record.quality) {
          stats.qualities[record.quality] = (stats.qualities[record.quality] || 0) + 1;
        }
      });

      return stats;
    } catch (error) {
      console.error('❌ Failed to get statistics:', error);
      return null;
    }
  }
}

export default DownloadTracker;
