import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';
import AsyncStorage from '@react-native-async-storage/async-storage';
import VideoDownloadService from './VideoDownloadService';
import DownloadTracker from './DownloadTracker';
import DownloadNotificationService from './DownloadNotificationService';

// Serviço para downloads em background
class BackgroundDownloadService {
  static activeDownloads = new Map(); // Controle de downloads ativos
  static downloadQueue = []; // Fila de downloads
  static isProcessing = false; // Flag para evitar processamento simultâneo
  static downloadLocks = new Map(); // Para prevenir race conditions

  // Iniciar download em background
  static async startBackgroundDownload(downloadInfo) {
    // Limpar downloads antigos primeiro
    await this.clearOldDownloads();

    const downloadId = this.generateDownloadId(downloadInfo);

    // Implementar lock para prevenir race conditions
    const lockKey = `${downloadId}_lock`;
    if (this.downloadLocks.has(lockKey)) {
      console.log('🚫 Download já sendo processado (lock ativo):', downloadId);
      throw new Error('Este download já está sendo processado');
    }

    // Criar lock
    this.downloadLocks.set(lockKey, true);

    try {
      // Verificar se já está baixando
      if (this.activeDownloads.has(downloadId)) {
        console.log('🚫 Download já em progresso:', downloadId);
        throw new Error('Este download já está em progresso');
      }

      // Verificar se já existe um download idêntico usando DownloadTracker
      const trackerCheck = await DownloadTracker.checkDownloadExists(downloadInfo);
      if (trackerCheck.exists) {
        console.log('🚫 Download duplicado detectado pelo DownloadTracker:', trackerCheck.reason);
        throw new Error('Este vídeo já foi baixado ou está sendo baixado');
      }

      // Verificação adicional com método original (fallback)
      const isDuplicate = await this.checkForDuplicateEnhanced(downloadInfo, downloadId);
      if (isDuplicate) {
        console.log('🚫 Download duplicado detectado:', downloadId);
        throw new Error('Este vídeo já foi baixado ou está sendo baixado');
      }

      // Adicionar à fila
      const downloadTask = {
        id: downloadId,
        ...downloadInfo,
        status: 'queued',
        progress: 0,
        startTime: new Date().toISOString(),
        error: null,
        normalizedUrl: this.normalizeVideoUrl(downloadInfo.url || downloadInfo.originalUrl)
      };

      this.downloadQueue.push(downloadTask);
      this.activeDownloads.set(downloadId, downloadTask);

      // Salvar estado
      await this.saveDownloadState();

      // Processar fila
      this.processDownloadQueue();

      return downloadId;

    } finally {
      // Remover lock
      this.downloadLocks.delete(lockKey);
    }
  }

  // Verificação de duplicata aprimorada
  static async checkForDuplicateEnhanced(downloadInfo, downloadId) {
    try {
      const originalUrl = downloadInfo.url || downloadInfo.originalUrl;
      const normalizedUrl = this.normalizeVideoUrl(originalUrl);
      const quality = downloadInfo.quality?.quality || 'default';

      console.log('🔍 Verificando duplicata aprimorada para:', { originalUrl, normalizedUrl, quality });

      // 1. Verificar downloads ativos com múltiplas comparações
      const activeCheck = await this.checkActiveDownloads(originalUrl, normalizedUrl, quality);
      if (activeCheck) {
        console.log('🚫 Duplicata encontrada em downloads ativos');
        return true;
      }

      // 2. Verificar histórico com verificação de arquivo
      const historyCheck = await this.checkDownloadHistory(originalUrl, normalizedUrl, quality);
      if (historyCheck) {
        console.log('🚫 Duplicata encontrada no histórico');
        return true;
      }

      // 3. Verificar por conteúdo similar (se possível)
      const contentCheck = await this.checkSimilarContent(downloadInfo);
      if (contentCheck) {
        console.log('🚫 Conteúdo similar encontrado');
        return true;
      }

      return false;
    } catch (error) {
      console.error('Erro na verificação de duplicata aprimorada:', error);
      return false;
    }
  }

  // Verificar se é um download duplicado (método original mantido para compatibilidade)
  static async checkForDuplicate(downloadInfo, downloadId) {
    try {
      const originalUrl = downloadInfo.url || downloadInfo.originalUrl;
      const normalizedUrl = this.normalizeVideoUrl(originalUrl);
      const quality = downloadInfo.quality?.quality || 'default';

      console.log('🔍 Verificando duplicata para:', { originalUrl, normalizedUrl, quality });

      // Verificar downloads ativos - usar múltiplas comparações
      for (const [activeId, activeTask] of this.activeDownloads) {
        const activeQuality = activeTask.quality?.quality || 'default';

        // Comparação 1: URLs normalizadas
        if (activeTask.normalizedUrl === normalizedUrl && activeQuality === quality) {
          console.log('🚫 Duplicata encontrada em downloads ativos (normalizada):', activeTask.normalizedUrl);
          return true;
        }

        // Comparação 2: URLs originais (fallback)
        if (activeTask.url === originalUrl && activeQuality === quality) {
          console.log('🚫 Duplicata encontrada em downloads ativos (original):', activeTask.url);
          return true;
        }

        // Comparação 3: IDs de download (mais rigorosa)
        if (activeId === downloadId) {
          console.log('🚫 Duplicata encontrada em downloads ativos (ID):', downloadId);
          return true;
        }
      }

      // Verificar histórico de downloads
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      const historyJson = await AsyncStorage.getItem('video_downloads');
      const history = historyJson ? JSON.parse(historyJson) : [];

      for (const download of history) {
        const downloadNormalizedUrl = this.normalizeVideoUrl(download.originalUrl);
        const downloadQuality = download.quality?.quality || 'default';

        // Comparação 1: URLs normalizadas
        if (downloadNormalizedUrl === normalizedUrl && downloadQuality === quality) {
          // Verificar se o arquivo ainda existe
          const FileSystem = require('expo-file-system');
          try {
            const fileInfo = await FileSystem.getInfoAsync(download.uri);
            if (fileInfo.exists && fileInfo.size > 10000) {
              console.log('🚫 Duplicata encontrada no histórico (normalizada):', downloadNormalizedUrl);
              return true;
            }
          } catch (e) {
            // Arquivo não existe, continuar verificação
          }
        }

        // Comparação 2: URLs originais (fallback)
        if (download.originalUrl === originalUrl && downloadQuality === quality) {
          const FileSystem = require('expo-file-system');
          try {
            const fileInfo = await FileSystem.getInfoAsync(download.uri);
            if (fileInfo.exists && fileInfo.size > 10000) {
              console.log('🚫 Duplicata encontrada no histórico (original):', download.originalUrl);
              return true;
            }
          } catch (e) {
            // Arquivo não existe, continuar verificação
          }
        }
      }

      console.log('✅ Nenhuma duplicata encontrada');
      return false;
    } catch (error) {
      console.error('Erro ao verificar duplicata:', error);
      return false;
    }
  }

  // Verificar downloads ativos
  static async checkActiveDownloads(originalUrl, normalizedUrl, quality) {
    for (const [id, download] of this.activeDownloads) {
      const downloadNormalizedUrl = this.normalizeVideoUrl(download.originalUrl || download.url);
      const downloadQuality = download.quality?.quality || 'default';

      // Comparação por URL normalizada e qualidade
      if (downloadNormalizedUrl === normalizedUrl && downloadQuality === quality) {
        return true;
      }

      // Comparação por URL original (fallback)
      if (download.originalUrl === originalUrl && downloadQuality === quality) {
        return true;
      }
    }
    return false;
  }

  // Verificar histórico de downloads
  static async checkDownloadHistory(originalUrl, normalizedUrl, quality) {
    try {
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      const historyJson = await AsyncStorage.getItem('video_downloads');
      const history = historyJson ? JSON.parse(historyJson) : [];

      for (const download of history) {
        const downloadNormalizedUrl = this.normalizeVideoUrl(download.originalUrl);
        const downloadQuality = download.quality?.quality || 'default';

        // Comparação 1: URLs normalizadas
        if (downloadNormalizedUrl === normalizedUrl && downloadQuality === quality) {
          // Verificar se o arquivo ainda existe
          const FileSystem = require('expo-file-system');
          try {
            const fileInfo = await FileSystem.getInfoAsync(download.uri);
            if (fileInfo.exists && fileInfo.size > 10000) {
              return true;
            }
          } catch (e) {
            // Arquivo não existe, continuar verificação
          }
        }

        // Comparação 2: URLs originais (fallback)
        if (download.originalUrl === originalUrl && downloadQuality === quality) {
          const FileSystem = require('expo-file-system');
          try {
            const fileInfo = await FileSystem.getInfoAsync(download.uri);
            if (fileInfo.exists && fileInfo.size > 10000) {
              return true;
            }
          } catch (e) {
            // Arquivo não existe, continuar verificação
          }
        }
      }

      return false;
    } catch (error) {
      console.error('Erro ao verificar histórico:', error);
      return false;
    }
  }

  // Verificar conteúdo similar (baseado em tamanho e metadados)
  static async checkSimilarContent(downloadInfo) {
    try {
      // Esta é uma verificação básica - pode ser expandida
      const title = downloadInfo.title?.toLowerCase();
      const duration = downloadInfo.duration;

      if (!title && !duration) return false;

      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      const historyJson = await AsyncStorage.getItem('video_downloads');
      const history = historyJson ? JSON.parse(historyJson) : [];

      for (const download of history) {
        // Comparar títulos se disponíveis
        if (title && download.title) {
          const downloadTitle = download.title.toLowerCase();
          const similarity = this.calculateStringSimilarity(title, downloadTitle);
          if (similarity > 0.9) { // 90% de similaridade
            return true;
          }
        }

        // Comparar duração se disponível
        if (duration && download.duration) {
          const durationDiff = Math.abs(duration - download.duration);
          if (durationDiff < 5) { // Diferença menor que 5 segundos
            return true;
          }
        }
      }

      return false;
    } catch (error) {
      console.error('Erro ao verificar conteúdo similar:', error);
      return false;
    }
  }

  // Calcular similaridade entre strings
  static calculateStringSimilarity(str1, str2) {
    if (!str1 || !str2) return 0;

    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 1.0;

    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  // Calcular distância de Levenshtein
  static levenshteinDistance(str1, str2) {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }

  // Gerar ID único para download baseado em URL normalizada
  static generateDownloadId(downloadInfo) {
    const url = downloadInfo.url || downloadInfo.originalUrl;
    const quality = downloadInfo.quality?.quality || 'default';

    // Normalizar URL para detectar duplicatas
    const normalizedUrl = this.normalizeVideoUrl(url);

    // Usar URL normalizada + qualidade para gerar ID consistente
    const idString = `${normalizedUrl}|${quality}`;
    const urlHash = this.generateUrlHash(idString);

    console.log('🆔 Gerando ID para:', { url, normalizedUrl, quality, hash: urlHash });

    return `${urlHash}_${quality}`.replace(/[^a-zA-Z0-9_]/g, '_').substring(0, 50);
  }

  // Normalizar URL para comparação de duplicatas
  static normalizeVideoUrl(url) {
    if (!url) return url;

    try {
      // Limpar URL básica primeiro
      let cleanUrl = url.trim();

      // Se não é uma URL válida, retornar como está
      if (!cleanUrl.startsWith('http')) {
        return cleanUrl;
      }

      const urlObj = new URL(cleanUrl);

      // Remover parâmetros desnecessários (mais abrangente)
      const paramsToRemove = [
        'utm_source', 'utm_medium', 'utm_campaign', 'utm_content', 'utm_term',
        'fbclid', 'gclid', 'msclkid', 'igshid', 'ref', 'ref_src', 'ref_url',
        't', 'si', 'feature', 'app', 'via', 'source', 'from'
      ];
      paramsToRemove.forEach(param => urlObj.searchParams.delete(param));

      // Normalizar YouTube URLs (mais robusto)
      if (urlObj.hostname.includes('youtube.com') || urlObj.hostname.includes('youtu.be')) {
        let videoId = null;

        if (urlObj.hostname.includes('youtu.be')) {
          videoId = urlObj.pathname.split('/')[1]?.split('?')[0];
        } else if (urlObj.searchParams.has('v')) {
          videoId = urlObj.searchParams.get('v');
        } else if (urlObj.pathname.includes('/embed/')) {
          videoId = urlObj.pathname.split('/embed/')[1]?.split('?')[0];
        }

        if (videoId && videoId.length === 11) {
          return `https://www.youtube.com/watch?v=${videoId}`;
        }
      }

      // Normalizar Vimeo URLs (mais robusto)
      if (urlObj.hostname.includes('vimeo.com')) {
        const pathParts = urlObj.pathname.split('/').filter(p => p);
        const videoId = pathParts.find(p => /^\d+$/.test(p));
        if (videoId) {
          return `https://vimeo.com/${videoId}`;
        }
      }

      // Normalizar outras plataformas conhecidas
      if (urlObj.hostname.includes('dailymotion.com')) {
        const videoMatch = urlObj.pathname.match(/\/video\/([^_?]+)/);
        if (videoMatch) {
          return `https://www.dailymotion.com/video/${videoMatch[1]}`;
        }
      }

      // Para URLs diretas de vídeo, manter estrutura básica
      if (/\.(mp4|webm|avi|mov|mkv|flv|wmv|m4v)(\?|$)/i.test(urlObj.pathname)) {
        return `${urlObj.protocol}//${urlObj.hostname}${urlObj.pathname}`;
      }

      // Para outras URLs, remover apenas parâmetros de tracking
      return urlObj.toString();
    } catch (e) {
      console.warn('Erro ao normalizar URL:', url, e.message);
      return url;
    }
  }

  // Gerar hash da URL para identificação única
  static generateUrlHash(input) {
    if (!input) return 'empty';

    // Usar algoritmo de hash mais robusto
    let hash = 0;
    const str = input.toString();

    if (str.length === 0) return 'empty';

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }

    // Garantir que o hash seja sempre positivo e consistente
    const positiveHash = Math.abs(hash);
    return positiveHash.toString(36).padStart(6, '0');
  }

  // Processar fila de downloads
  static async processDownloadQueue() {
    if (this.isProcessing) return;
    this.isProcessing = true;

    try {
      while (this.downloadQueue.length > 0) {
        const downloadTask = this.downloadQueue.shift();
        
        if (!downloadTask) continue;

        console.log(`🚀 Iniciando download em background: ${downloadTask.id}`);
        
        try {
          await this.executeDownload(downloadTask);
        } catch (error) {
          console.error(`❌ Erro no download ${downloadTask.id}:`, error);
          downloadTask.status = 'error';
          downloadTask.error = error.message;
          await this.saveDownloadState();
        }
      }
    } finally {
      this.isProcessing = false;
    }
  }

  // Executar download individual
  static async executeDownload(downloadTask) {
    try {
      downloadTask.status = 'downloading';
      await this.saveDownloadState();

      // Gerar nome do arquivo
      const fileName = this.generateFileName(downloadTask);
      const fileUri = FileSystem.documentDirectory + fileName;

      // Callback de progresso
      const progressCallback = async (progress) => {
        const percentage = Math.round((progress.totalBytesWritten / progress.totalBytesExpectedToWrite) * 100);
        downloadTask.progress = percentage;
        downloadTask.status = 'downloading';

        // Salvar progresso a cada 10%
        if (percentage % 10 === 0) {
          await this.saveDownloadState();
        }
      };

      // Usar VideoDownloadService para fazer o download real
      const result = await VideoDownloadService.downloadVideo(
        downloadTask.url,
        progressCallback
      );

      if (!result || !result.uri) {
        throw new Error('Falha no download do arquivo');
      }

      // Salvar na galeria usando o VideoDownloadService
      try {
        await VideoDownloadService.saveToGallery(result.uri, result.fileName || fileName);
      } catch (galleryError) {
        // Se falhar ao salvar na galeria, continuar mesmo assim
        console.warn('Falha ao salvar na galeria:', galleryError.message);
      }

      // Atualizar status
      downloadTask.status = 'completed';
      downloadTask.progress = 100;
      downloadTask.fileUri = result.uri;
      downloadTask.fileName = result.fileName || fileName;
      downloadTask.fileSize = result.size;
      downloadTask.completedTime = new Date().toISOString();

      // Adicionar ao histórico de downloads
      await this.addToDownloadHistory(downloadTask);

      // Adicionar ao DownloadTracker
      await DownloadTracker.addDownloadRecord(downloadTask);

    } catch (error) {
      downloadTask.status = 'error';
      downloadTask.error = error.message;
      throw error;
    } finally {
      await this.saveDownloadState();
    }
  }

  // Gerar nome do arquivo
  static generateFileName(downloadTask) {
    const quality = downloadTask.quality?.quality || 'default';
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const extension = this.getFileExtension(downloadTask.url);
    
    return `video_${quality}_${timestamp}.${extension}`;
  }

  // Obter extensão do arquivo
  static getFileExtension(url) {
    if (url.includes('.mp4')) return 'mp4';
    if (url.includes('.webm')) return 'webm';
    if (url.includes('.avi')) return 'avi';
    if (url.includes('.mov')) return 'mov';
    return 'mp4'; // padrão
  }

  // Salvar na galeria
  static async saveToGallery(fileUri, fileName) {
    try {
      const asset = await MediaLibrary.createAssetAsync(fileUri);
      const album = await MediaLibrary.getAlbumAsync('Video Downloads');
      
      if (album == null) {
        await MediaLibrary.createAlbumAsync('Video Downloads', asset, false);
      } else {
        await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
      }
      
      console.log('✅ Vídeo salvo na galeria:', fileName);
    } catch (error) {
      console.error('❌ Erro ao salvar na galeria:', error);
      throw error;
    }
  }

  // Salvar estado dos downloads
  static async saveDownloadState() {
    try {
      const state = {
        activeDownloads: Array.from(this.activeDownloads.values()),
        queue: this.downloadQueue,
        lastUpdate: new Date().toISOString()
      };
      
      await AsyncStorage.setItem('background_downloads', JSON.stringify(state));
    } catch (error) {
      console.error('Erro ao salvar estado dos downloads:', error);
    }
  }

  // Carregar estado dos downloads
  static async loadDownloadState() {
    try {
      const stateJson = await AsyncStorage.getItem('background_downloads');
      if (stateJson) {
        const state = JSON.parse(stateJson);
        
        // Restaurar downloads ativos
        this.activeDownloads.clear();
        state.activeDownloads?.forEach(download => {
          this.activeDownloads.set(download.id, download);
        });
        
        // Restaurar fila (apenas downloads pendentes)
        this.downloadQueue = state.queue?.filter(d => d.status === 'queued') || [];
        
        console.log(`📂 Estado restaurado: ${this.activeDownloads.size} downloads ativos`);
      }
    } catch (error) {
      console.error('Erro ao carregar estado dos downloads:', error);
    }
  }

  // Adicionar ao histórico
  static async addToDownloadHistory(downloadTask) {
    try {
      const historyJson = await AsyncStorage.getItem('video_downloads');
      const history = historyJson ? JSON.parse(historyJson) : [];
      
      const historyItem = {
        id: downloadTask.id,
        fileName: downloadTask.fileName,
        uri: downloadTask.fileUri,
        size: downloadTask.fileSize,
        quality: downloadTask.quality,
        originalUrl: downloadTask.originalUrl,
        timestamp: downloadTask.completedTime,
        status: 'completed'
      };
      
      history.unshift(historyItem);
      await AsyncStorage.setItem('video_downloads', JSON.stringify(history.slice(0, 100))); // Manter apenas 100
      
    } catch (error) {
      console.error('Erro ao adicionar ao histórico:', error);
    }
  }

  // Obter status de todos os downloads
  static getDownloadStatus() {
    return {
      active: Array.from(this.activeDownloads.values()),
      queue: this.downloadQueue.length,
      processing: this.isProcessing
    };
  }

  // Cancelar download
  static async cancelDownload(downloadId) {
    const download = this.activeDownloads.get(downloadId);
    if (download) {
      download.status = 'cancelled';
      this.activeDownloads.delete(downloadId);
      
      // Remover da fila se estiver lá
      this.downloadQueue = this.downloadQueue.filter(d => d.id !== downloadId);
      
      await this.saveDownloadState();
      return true;
    }
    return false;
  }

  // Limpar downloads concluídos
  static async clearCompletedDownloads() {
    const completed = Array.from(this.activeDownloads.values()).filter(d =>
      d.status === 'completed' || d.status === 'error'
    );

    completed.forEach(download => {
      this.activeDownloads.delete(download.id);
    });

    await this.saveDownloadState();
    return completed.length;
  }

  // Limpar downloads antigos e órfãos (melhorado)
  static async clearOldDownloads() {
    const now = Date.now();
    const maxAge = 30 * 60 * 1000; // 30 minutos (reduzido de 1 hora)
    const stuckThreshold = 10 * 60 * 1000; // 10 minutos para downloads travados

    let removedCount = 0;

    // Limpar downloads ativos antigos, travados ou concluídos
    const toRemove = [];
    for (const [id, download] of this.activeDownloads) {
      const startTime = new Date(download.startTime).getTime();
      const isOld = now - startTime > maxAge;
      const isStuck = download.status === 'downloading' && now - startTime > stuckThreshold;
      const isCompleted = download.status === 'completed' || download.status === 'error';

      if (isOld || isStuck || isCompleted) {
        toRemove.push(id);
        console.log(`🧹 Removendo download ${isOld ? 'antigo' : isStuck ? 'travado' : 'concluído'}:`, id);
      }
    }

    toRemove.forEach(id => {
      this.activeDownloads.delete(id);
      removedCount++;
    });

    // Limpar fila de downloads órfãos
    const originalQueueLength = this.downloadQueue.length;
    this.downloadQueue = this.downloadQueue.filter(download => {
      const startTime = new Date(download.startTime).getTime();
      const isValid = now - startTime <= maxAge &&
                     download.status !== 'completed' &&
                     download.status !== 'error';
      return isValid;
    });

    removedCount += originalQueueLength - this.downloadQueue.length;

    // Limpar locks órfãos
    this.downloadLocks.clear();

    if (removedCount > 0) {
      console.log(`🧹 Limpeza concluída: ${removedCount} downloads removidos`);
      await this.saveDownloadState();
    }
  }

  // Inicializar serviço
  static async initialize() {
    await this.loadDownloadState();
    
    // Retomar downloads pendentes
    if (this.downloadQueue.length > 0) {
      console.log(`🔄 Retomando ${this.downloadQueue.length} downloads pendentes`);
      this.processDownloadQueue();
    }
  }
}

export default BackgroundDownloadService;
