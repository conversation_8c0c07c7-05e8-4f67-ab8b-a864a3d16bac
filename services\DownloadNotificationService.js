/**
 * Download Notification Service
 * Provides user feedback for download operations and duplicate detection
 */

import { Alert, ToastAndroid, Platform } from 'react-native';

class DownloadNotificationService {
  
  // Show duplicate detection notification
  static showDuplicateDetected(reason, existingDownload) {
    const messages = {
      'url_quality_match': 'Este vídeo já foi baixado na mesma qualidade',
      'content_hash_match': 'Um vídeo com conteúdo idêntico já foi baixado',
      'title_similarity': 'Um vídeo com título similar já foi baixado',
      'active_download': 'Este download já está em progresso',
      'default': 'Este vídeo já foi baixado anteriormente'
    };

    const message = messages[reason] || messages.default;
    const title = '🚫 Download Duplicado';

    if (existingDownload) {
      const details = [
        `Arquivo: ${existingDownload.fileName || 'N/A'}`,
        `Qualidade: ${existingDownload.quality || 'N/A'}`,
        `Data: ${this.formatDate(existingDownload.timestamp)}`
      ].join('\n');

      Alert.alert(
        title,
        `${message}\n\nDetalhes do download existente:\n${details}`,
        [
          { text: 'OK', style: 'default' },
          {
            text: 'Ver Downloads',
            onPress: () => this.navigateToDownloads()
          }
        ]
      );
    } else {
      this.showToast(`${title}: ${message}`);
    }
  }

  // Show download started notification
  static showDownloadStarted(downloadInfo) {
    const title = '🚀 Download Iniciado';
    const quality = downloadInfo.quality?.label || downloadInfo.quality?.quality || 'Padrão';
    const message = `Download em segundo plano iniciado!\n\nQualidade: ${quality}\nVocê pode continuar navegando.`;

    Alert.alert(
      title,
      message,
      [
        { text: 'OK', style: 'default' },
        {
          text: 'Ver Progresso',
          onPress: () => this.navigateToDownloads()
        }
      ]
    );
  }

  // Show download completed notification
  static showDownloadCompleted(downloadInfo) {
    const title = '✅ Download Concluído';
    const fileName = downloadInfo.fileName || 'Vídeo';
    const size = this.formatFileSize(downloadInfo.fileSize || downloadInfo.size);
    const message = `${fileName}\nTamanho: ${size}`;

    Alert.alert(
      title,
      message,
      [
        { text: 'OK', style: 'default' },
        {
          text: 'Abrir Pasta',
          onPress: () => this.openDownloadsFolder()
        },
        {
          text: 'Ver Downloads',
          onPress: () => this.navigateToDownloads()
        }
      ]
    );
  }

  // Show download failed notification
  static showDownloadFailed(error, downloadInfo) {
    const title = '❌ Erro no Download';
    const message = error.message || 'Não foi possível completar o download';

    Alert.alert(
      title,
      message,
      [
        { text: 'OK', style: 'default' },
        {
          text: 'Tentar Novamente',
          onPress: () => this.retryDownload(downloadInfo)
        }
      ]
    );
  }

  // Show download progress notification (for long downloads)
  static showDownloadProgress(downloadInfo, progress) {
    if (progress % 25 === 0) { // Show every 25%
      const message = `Download ${progress}% concluído: ${downloadInfo.fileName || 'Vídeo'}`;
      this.showToast(message);
    }
  }

  // Show storage space warning
  static showStorageWarning(requiredSpace, availableSpace) {
    const title = '⚠️ Espaço Insuficiente';
    const message = `Espaço necessário: ${this.formatFileSize(requiredSpace)}\nEspaço disponível: ${this.formatFileSize(availableSpace)}\n\nLibere espaço e tente novamente.`;

    Alert.alert(
      title,
      message,
      [
        { text: 'OK', style: 'default' },
        {
          text: 'Gerenciar Armazenamento',
          onPress: () => this.openStorageSettings()
        }
      ]
    );
  }

  // Show permission denied notification
  static showPermissionDenied() {
    const title = '🔒 Permissão Necessária';
    const message = 'É necessário conceder permissão de armazenamento para baixar vídeos.';

    Alert.alert(
      title,
      message,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Configurações',
          onPress: () => this.openAppSettings()
        }
      ]
    );
  }

  // Show network error notification
  static showNetworkError() {
    const title = '🌐 Erro de Conexão';
    const message = 'Verifique sua conexão com a internet e tente novamente.';

    Alert.alert(
      title,
      message,
      [
        { text: 'OK', style: 'default' },
        {
          text: 'Tentar Novamente',
          onPress: () => this.retryLastDownload()
        }
      ]
    );
  }

  // Show cleanup notification
  static showCleanupCompleted(removedCount, freedSpace) {
    const title = '🧹 Limpeza Concluída';
    const message = `${removedCount} downloads antigos removidos\nEspaço liberado: ${this.formatFileSize(freedSpace)}`;

    this.showToast(`${title}: ${message}`);
  }

  // Show toast message (cross-platform)
  static showToast(message) {
    if (Platform.OS === 'android') {
      ToastAndroid.show(message, ToastAndroid.LONG);
    } else {
      // For iOS, use a simple alert
      Alert.alert('Informação', message, [{ text: 'OK' }]);
    }
  }

  // Format file size
  static formatFileSize(bytes) {
    if (!bytes || bytes === 0) return '0 B';
    
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  }

  // Format date
  static formatDate(dateString) {
    if (!dateString) return 'N/A';
    
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'N/A';
    }
  }

  // Navigation helpers (to be implemented by the app)
  static navigateToDownloads() {
    // This should be implemented by the app to navigate to downloads screen
    console.log('Navigate to downloads screen');
  }

  static openDownloadsFolder() {
    // This should be implemented by the app to open downloads folder
    console.log('Open downloads folder');
  }

  static openStorageSettings() {
    // This should be implemented by the app to open storage settings
    console.log('Open storage settings');
  }

  static openAppSettings() {
    // This should be implemented by the app to open app settings
    console.log('Open app settings');
  }

  static retryDownload(downloadInfo) {
    // This should be implemented by the app to retry download
    console.log('Retry download:', downloadInfo);
  }

  static retryLastDownload() {
    // This should be implemented by the app to retry last download
    console.log('Retry last download');
  }

  // Show download statistics
  static async showDownloadStatistics() {
    try {
      const DownloadTracker = require('./DownloadTracker').default;
      const stats = await DownloadTracker.getStatistics();
      
      if (!stats) {
        this.showToast('Não foi possível carregar estatísticas');
        return;
      }

      const message = [
        `Total de downloads: ${stats.totalDownloads}`,
        `Últimas 24h: ${stats.last24Hours}`,
        `Últimos 7 dias: ${stats.last7Days}`,
        `Últimos 30 dias: ${stats.last30Days}`,
        `Espaço total: ${this.formatFileSize(stats.totalSize)}`
      ].join('\n');

      Alert.alert('📊 Estatísticas de Download', message, [{ text: 'OK' }]);
    } catch (error) {
      console.error('Failed to show statistics:', error);
      this.showToast('Erro ao carregar estatísticas');
    }
  }

  // Show quality selection help
  static showQualitySelectionHelp() {
    const title = '💡 Seleção de Qualidade';
    const message = [
      '• 4K/2160p: Máxima qualidade (arquivo grande)',
      '• 1080p: Alta qualidade (recomendado)',
      '• 720p: Boa qualidade (arquivo médio)',
      '• 480p: Qualidade padrão (arquivo pequeno)',
      '• 360p: Baixa qualidade (economia de dados)',
      '',
      'Qualidades maiores ocupam mais espaço mas oferecem melhor imagem.'
    ].join('\n');

    Alert.alert(title, message, [{ text: 'Entendi' }]);
  }

  // Show batch download confirmation
  static showBatchDownloadConfirmation(count, totalSize) {
    const title = '📦 Download em Lote';
    const message = `Você está prestes a baixar ${count} vídeos.\nTamanho total estimado: ${this.formatFileSize(totalSize)}\n\nDeseja continuar?`;

    return new Promise((resolve) => {
      Alert.alert(
        title,
        message,
        [
          { text: 'Cancelar', style: 'cancel', onPress: () => resolve(false) },
          { text: 'Baixar Todos', onPress: () => resolve(true) }
        ]
      );
    });
  }
}

export default DownloadNotificationService;
