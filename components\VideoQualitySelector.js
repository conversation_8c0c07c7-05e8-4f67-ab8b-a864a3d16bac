import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import VideoExtractorService from '../services/VideoExtractorService';

const { width, height } = Dimensions.get('window');

const VideoQualitySelector = ({ 
  visible, 
  onClose, 
  videoUrl, 
  onQualitySelected,
  onDownloadStart 
}) => {
  const [qualities, setQualities] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedQuality, setSelectedQuality] = useState(null);

  useEffect(() => {
    if (visible && videoUrl) {
      detectVideoQualities();
    }
  }, [visible, videoUrl]);

  const detectVideoQualities = async () => {
    setLoading(true);
    try {
      console.log('🎯 Detectando vídeo principal e qualidades para:', videoUrl);

      // Usar o novo serviço de extração otimizado
      const extractedVideos = await VideoExtractorService.extractVideo(videoUrl);
      console.log(`✅ Encontrados ${extractedVideos.length} vídeos/qualidades`);

      if (extractedVideos && extractedVideos.length > 0) {
        // Converter para formato esperado e adicionar informações visuais
        const detectedQualities = extractedVideos.map((video, index) => {
          const qualityInfo = {
            quality: video.quality,
            label: `${video.quality}${video.hasAudio ? ' (com áudio)' : ' (apenas vídeo)'}`,
            size: video.filesize ? `${(video.filesize / 1024 / 1024).toFixed(1)} MB` : 'Tamanho variável',
            url: video.url,
            format: getFormatFromUrl(video.url),
            priority: getPriorityFromQuality(video.quality),
            hasAudio: video.hasAudio,
            isMainVideo: index === 0 // Primeiro é o principal
          };

          console.log(`📹 Qualidade ${index + 1}: ${qualityInfo.quality} - ${qualityInfo.size}`);
          return qualityInfo;
        });

        // Ordenar por prioridade (melhor qualidade primeiro)
        const sortedQualities = detectedQualities.sort((a, b) => a.priority - b.priority);
        setQualities(sortedQualities);

        // Selecionar qualidade padrão (melhor com áudio, ou melhor disponível)
        const bestWithAudio = sortedQualities.find(q => q.hasAudio);
        const selectedQuality = bestWithAudio || sortedQualities[0];
        setSelectedQuality(selectedQuality);

        console.log(`🎯 Qualidade selecionada: ${selectedQuality.quality}`);
      } else {
        console.log('⚠️ Nenhum vídeo encontrado, usando fallback inteligente');
        // Fallback inteligente - tentar diferentes estratégias
        await handleFallbackDetection();
      }
    } catch (error) {
      console.error('❌ Erro ao detectar qualidades:', error);
      // Fallback em caso de erro
      await handleFallbackDetection();
    } finally {
      setLoading(false);
    }
  };

  // Função de fallback inteligente
  const handleFallbackDetection = async () => {
    try {
      console.log('🔄 Tentando fallback inteligente...');

      // Estratégia 1: Tentar análise básica da URL
      let fallbackQualities = await analyzeVideoQualities(videoUrl);

      // Estratégia 2: Se falhar, usar qualidades padrão baseadas na URL
      if (!fallbackQualities || fallbackQualities.length === 0) {
        console.log('🔄 Usando qualidades padrão...');
        fallbackQualities = getDefaultQualities(videoUrl);
      }

      // Estratégia 3: Se ainda falhar, criar qualidade genérica
      if (!fallbackQualities || fallbackQualities.length === 0) {
        console.log('🔄 Criando qualidade genérica...');
        fallbackQualities = [{
          quality: 'Padrão',
          label: 'Qualidade Padrão',
          size: 'Tamanho variável',
          url: videoUrl, // Usar a URL original
          format: 'mp4',
          priority: 5,
          hasAudio: true,
          isMainVideo: true
        }];
      }

      setQualities(fallbackQualities);
      if (fallbackQualities.length > 0) {
        setSelectedQuality(fallbackQualities[0]);
        console.log(`🎯 Fallback selecionado: ${fallbackQualities[0].quality}`);
      }
    } catch (fallbackError) {
      console.error('❌ Erro no fallback:', fallbackError);
      // Último recurso - qualidade mínima
      const emergencyQuality = [{
        quality: 'Original',
        label: 'Qualidade Original',
        size: 'Tamanho desconhecido',
        url: videoUrl,
        format: 'mp4',
        priority: 10,
        hasAudio: true,
        isMainVideo: true
      }];
      setQualities(emergencyQuality);
      setSelectedQuality(emergencyQuality[0]);
      console.log('🆘 Usando qualidade de emergência');
    }
  };

  const analyzeVideoQualities = async (url) => {
    try {
      // Tentar diferentes estratégias para detectar qualidades
      const strategies = [
        () => detectYouTubeQualities(url),
        () => detectVimeoQualities(url),
        () => detectGenericQualities(url),
        () => getDefaultQualities(url)
      ];

      for (const strategy of strategies) {
        try {
          const result = await strategy();
          if (result && result.length > 0) {
            return result;
          }
        } catch (e) {
          console.log('Estratégia falhou:', e.message);
          continue;
        }
      }

      // Fallback: qualidades padrão
      return getDefaultQualities(url);
    } catch (error) {
      console.error('Erro na análise de qualidades:', error);
      return getDefaultQualities(url);
    }
  };

  const detectYouTubeQualities = async (url) => {
    if (!url.includes('youtube.com') && !url.includes('youtu.be')) {
      throw new Error('Não é YouTube');
    }

    try {
      // Extrair video ID
      let videoId = null;
      if (url.includes('youtube.com/watch')) {
        const urlObj = new URL(url);
        videoId = urlObj.searchParams.get('v');
      } else if (url.includes('youtu.be/')) {
        videoId = url.split('youtu.be/')[1].split('?')[0];
      }

      if (!videoId) {
        throw new Error('ID do vídeo não encontrado');
      }

      // Tentar obter informações reais do vídeo
      const response = await fetch(`https://www.youtube.com/watch?v=${videoId}`, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      const html = await response.text();

      // Procurar por configurações do player do YouTube
      const playerConfigMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?});/);
      if (playerConfigMatch) {
        try {
          const playerConfig = JSON.parse(playerConfigMatch[1]);
          const streamingData = playerConfig.streamingData;

          if (streamingData && (streamingData.formats || streamingData.adaptiveFormats)) {
            const foundQualities = [];

            // Formatos progressivos (vídeo + áudio)
            if (streamingData.formats) {
              streamingData.formats.forEach(format => {
                if (format.url && format.qualityLabel) {
                  foundQualities.push({
                    quality: format.qualityLabel,
                    label: `${format.qualityLabel} (${format.mimeType?.split(';')[0] || 'mp4'})`,
                    url: format.url,
                    size: format.contentLength ? `${(format.contentLength / 1024 / 1024).toFixed(1)} MB` : 'Tamanho variável',
                    format: format.mimeType?.includes('mp4') ? 'mp4' : 'webm',
                    priority: getPriorityFromQuality(format.qualityLabel)
                  });
                }
              });
            }

            // Formatos adaptativos (apenas vídeo, melhor qualidade)
            if (streamingData.adaptiveFormats && foundQualities.length === 0) {
              const videoFormats = streamingData.adaptiveFormats.filter(f =>
                f.mimeType?.includes('video') && f.url && f.qualityLabel
              );

              videoFormats.forEach(format => {
                foundQualities.push({
                  quality: format.qualityLabel,
                  label: `${format.qualityLabel} (apenas vídeo)`,
                  url: format.url,
                  size: format.contentLength ? `${(format.contentLength / 1024 / 1024).toFixed(1)} MB` : 'Tamanho variável',
                  format: format.mimeType?.includes('mp4') ? 'mp4' : 'webm',
                  priority: getPriorityFromQuality(format.qualityLabel)
                });
              });
            }

            if (foundQualities.length > 0) {
              return foundQualities.sort((a, b) => a.priority - b.priority);
            }
          }
        } catch (e) {
          console.log('Erro ao analisar configuração do player:', e);
        }
      }

      // Fallback: procurar por padrões alternativos
      const alternativePatterns = [
        /"url":"([^"]*)"[^}]*"qualityLabel":"([^"]*)"/g,
        /"qualityLabel":"([^"]*)"[^}]*"url":"([^"]*)"/g
      ];

      const qualityMap = new Map();
      for (const pattern of alternativePatterns) {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          let videoUrl, quality;

          if (pattern.source.includes('url.*qualityLabel')) {
            videoUrl = match[1];
            quality = match[2];
          } else {
            quality = match[1];
            videoUrl = match[2];
          }

          if (quality && videoUrl && !qualityMap.has(quality)) {
            qualityMap.set(quality, {
              quality: quality,
              label: quality,
              url: decodeURIComponent(videoUrl.replace(/\\u0026/g, '&').replace(/\\/g, '')),
              format: 'mp4',
              priority: getPriorityFromQuality(quality)
            });
          }
        }
      }

      if (qualityMap.size > 0) {
        return Array.from(qualityMap.values()).sort((a, b) => a.priority - b.priority);
      }

      // Fallback para qualidades padrão se não conseguir detectar
      return getStandardYouTubeQualities(url);

    } catch (error) {
      console.log('Erro na detecção do YouTube:', error);
      return getStandardYouTubeQualities(url);
    }
  };

  const getStandardYouTubeQualities = (url) => {
    return [
      {
        quality: '2160p',
        label: '2160p 4K',
        size: 'Estimado: ~500MB',
        url: url,
        format: 'mp4',
        priority: 1
      },
      {
        quality: '1440p',
        label: '1440p QHD',
        size: 'Estimado: ~300MB',
        url: url,
        format: 'mp4',
        priority: 2
      },
      {
        quality: '1080p',
        label: '1080p Full HD',
        size: 'Estimado: ~150MB',
        url: url,
        format: 'mp4',
        priority: 3
      },
      {
        quality: '720p',
        label: '720p HD',
        size: 'Estimado: ~80MB',
        url: url,
        format: 'mp4',
        priority: 4
      },
      {
        quality: '480p',
        label: '480p',
        size: 'Estimado: ~40MB',
        url: url,
        format: 'mp4',
        priority: 5
      },
      {
        quality: '360p',
        label: '360p',
        size: 'Estimado: ~20MB',
        url: url,
        format: 'mp4',
        priority: 6
      },
      {
        quality: '240p',
        label: '240p',
        size: 'Estimado: ~10MB',
        url: url,
        format: 'mp4',
        priority: 7
      }
    ];
  };

  const getPriorityFromQuality = (quality) => {
    const qualityOrder = {
      '2160p': 1, '4K': 1,
      '1440p': 2, 'QHD': 2,
      '1080p': 3, 'Full HD': 3,
      '720p': 4, 'HD': 4,
      '480p': 5,
      '360p': 6,
      '240p': 7,
      '144p': 8
    };

    for (const [key, priority] of Object.entries(qualityOrder)) {
      if (quality.includes(key)) {
        return priority;
      }
    }

    return 9; // Qualidade desconhecida
  };

  const detectVimeoQualities = async (url) => {
    if (!url.includes('vimeo.com')) {
      throw new Error('Não é Vimeo');
    }

    try {
      // Extrair video ID do Vimeo
      const videoId = url.match(/vimeo\.com\/(\d+)/)?.[1];
      if (!videoId) {
        throw new Error('ID do vídeo Vimeo não encontrado');
      }

      // Tentar obter informações do vídeo via API pública do Vimeo
      const response = await fetch(`https://vimeo.com/${videoId}`, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      const html = await response.text();

      // Procurar por configurações de qualidade no HTML
      const configMatch = html.match(/"config_url":"([^"]*)"/);
      if (configMatch) {
        try {
          const configUrl = configMatch[1].replace(/\\u002F/g, '/').replace(/\\/g, '');
          const configResponse = await fetch(configUrl);
          const config = await configResponse.json();

          if (config.request && config.request.files) {
            const qualities = [];

            // Tentar formatos progressivos primeiro (vídeo + áudio)
            if (config.request.files.progressive) {
              config.request.files.progressive.forEach(file => {
                qualities.push({
                  quality: `${file.height}p`,
                  label: `${file.height}p ${file.height >= 1080 ? 'Full HD' : file.height >= 720 ? 'HD' : ''}`.trim(),
                  size: file.size ? `${(file.size / 1024 / 1024).toFixed(1)} MB` : 'Tamanho variável',
                  url: file.url,
                  format: 'mp4',
                  priority: getPriorityFromQuality(`${file.height}p`)
                });
              });
            }

            // Se não houver progressivos, tentar DASH (apenas vídeo)
            if (qualities.length === 0 && config.request.files.dash) {
              const dashVideo = config.request.files.dash.streams?.filter(s => s.profile?.includes('video'));
              if (dashVideo) {
                dashVideo.forEach(stream => {
                  if (stream.url) {
                    qualities.push({
                      quality: `${stream.height || 'Unknown'}p`,
                      label: `${stream.height || 'Unknown'}p (apenas vídeo)`,
                      size: 'Tamanho variável',
                      url: stream.url,
                      format: 'mp4',
                      priority: getPriorityFromQuality(`${stream.height || 999}p`)
                    });
                  }
                });
              }
            }

            if (qualities.length > 0) {
              return qualities.sort((a, b) => a.priority - b.priority);
            }
          }
        } catch (e) {
          console.log('Erro ao obter config do Vimeo:', e);
        }
      }

      // Procurar por padrões alternativos no HTML do Vimeo
      const vimeoPatterns = [
        /"progressive":\s*\[([^\]]+)\]/g,
        /"url":"([^"]*\.mp4[^"]*)"[^}]*"height":(\d+)/g,
        /"height":(\d+)[^}]*"url":"([^"]*\.mp4[^"]*)"/g
      ];

      const foundQualities = [];
      for (const pattern of vimeoPatterns) {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          if (pattern.source.includes('progressive')) {
            // Analisar array de qualidades progressivas
            try {
              const progressiveData = JSON.parse(`[${match[1]}]`);
              progressiveData.forEach(item => {
                if (item.url && item.height) {
                  foundQualities.push({
                    quality: `${item.height}p`,
                    label: `${item.height}p ${item.height >= 1080 ? 'Full HD' : item.height >= 720 ? 'HD' : ''}`.trim(),
                    size: item.size ? `${(item.size / 1024 / 1024).toFixed(1)} MB` : 'Tamanho variável',
                    url: item.url,
                    format: 'mp4',
                    priority: getPriorityFromQuality(`${item.height}p`)
                  });
                }
              });
            } catch (e) {
              console.log('Erro ao analisar dados progressivos:', e);
            }
          } else {
            // Padrões individuais
            let url, height;
            if (match[1] && match[2]) {
              if (match[1].includes('mp4')) {
                url = match[1];
                height = match[2];
              } else {
                height = match[1];
                url = match[2];
              }

              foundQualities.push({
                quality: `${height}p`,
                label: `${height}p ${height >= 1080 ? 'Full HD' : height >= 720 ? 'HD' : ''}`.trim(),
                size: 'Tamanho variável',
                url: url,
                format: 'mp4',
                priority: getPriorityFromQuality(`${height}p`)
              });
            }
          }
        }
      }

      if (foundQualities.length > 0) {
        // Remover duplicatas
        const uniqueQualities = foundQualities.filter((quality, index, self) =>
          index === self.findIndex(q => q.quality === quality.quality)
        );
        return uniqueQualities.sort((a, b) => a.priority - b.priority);
      }

      // Fallback para qualidades padrão
      return getStandardVimeoQualities(url);

    } catch (error) {
      console.log('Erro na detecção do Vimeo:', error);
      return getStandardVimeoQualities(url);
    }
  };

  const getStandardVimeoQualities = (url) => {
    return [
      {
        quality: '1080p',
        label: '1080p Full HD',
        size: 'Estimado: ~150MB',
        url: url,
        format: 'mp4',
        priority: 1
      },
      {
        quality: '720p',
        label: '720p HD',
        size: 'Estimado: ~80MB',
        url: url,
        format: 'mp4',
        priority: 2
      },
      {
        quality: '540p',
        label: '540p',
        size: 'Estimado: ~50MB',
        url: url,
        format: 'mp4',
        priority: 3
      },
      {
        quality: '360p',
        label: '360p',
        size: 'Estimado: ~25MB',
        url: url,
        format: 'mp4',
        priority: 4
      }
    ];
  };

  const detectGenericQualities = async (url) => {
    try {
      // Primeiro, tentar detectar se é um site conhecido com múltiplas qualidades
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      const html = await response.text();

      // Procurar por múltiplas qualidades no HTML com padrões mais abrangentes
      const qualityPatterns = [
        // Padrões para sites de vídeo genéricos
        /"url":"([^"]*\.mp4[^"]*)"[^}]*"quality":"([^"]*)"/g,
        /"quality":"([^"]*)"[^}]*"url":"([^"]*\.mp4[^"]*)"/g,
        /"(\d+p)"[^}]*"url":"([^"]*\.mp4[^"]*)"/g,
        /"url":"([^"]*\.mp4[^"]*)"[^}]*"height":(\d+)/g,
        /"height":(\d+)[^}]*"url":"([^"]*\.mp4[^"]*)"/g,
        /src="([^"]*\.mp4[^"]*)"[^>]*data-quality="(\d+p?)"/g,
        /data-quality="(\d+p?)"[^>]*src="([^"]*\.mp4[^"]*)"/g,
        /"file":"([^"]*\.mp4[^"]*)"[^}]*"label":"([^"]*)"/g,
        /"label":"([^"]*)"[^}]*"file":"([^"]*\.mp4[^"]*)"/g,
        // Padrões para M3U8 e outros formatos
        /"url":"([^"]*\.m3u8[^"]*)"[^}]*"quality":"([^"]*)"/g,
        /"quality":"([^"]*)"[^}]*"url":"([^"]*\.m3u8[^"]*)"/g
      ];

      const foundQualities = new Map();

      for (const pattern of qualityPatterns) {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          let quality, videoUrl;

          // Determinar qual é a qualidade e qual é a URL baseado no padrão
          if (pattern.source.includes('url.*quality') || pattern.source.includes('url.*height')) {
            videoUrl = match[1];
            quality = match[2];
          } else if (pattern.source.includes('quality.*url') || pattern.source.includes('height.*url')) {
            quality = match[1];
            videoUrl = match[2];
          } else if (pattern.source.includes('src.*data-quality')) {
            videoUrl = match[1];
            quality = match[2];
          } else if (pattern.source.includes('data-quality.*src')) {
            quality = match[1];
            videoUrl = match[2];
          } else if (pattern.source.includes('file.*label')) {
            videoUrl = match[1];
            quality = match[2];
          } else if (pattern.source.includes('label.*file')) {
            quality = match[1];
            videoUrl = match[2];
          }

          // Normalizar qualidade
          if (quality && !isNaN(quality)) {
            quality = quality + 'p';
          }

          // Verificar se é uma URL válida de vídeo
          if (videoUrl && quality && (videoUrl.startsWith('http') || videoUrl.startsWith('//'))) {
            if (videoUrl.startsWith('//')) {
              videoUrl = 'https:' + videoUrl;
            }

            // Decodificar URL se necessário
            try {
              videoUrl = decodeURIComponent(videoUrl.replace(/\\u0026/g, '&').replace(/\\/g, ''));
            } catch (e) {
              // Se falhar na decodificação, usar URL original
            }

            foundQualities.set(quality, {
              quality: quality,
              label: `${quality} ${getQualityLabel(quality)}`,
              size: 'Tamanho variável',
              url: videoUrl,
              format: getFormatFromUrl(videoUrl),
              priority: getPriorityFromQuality(quality)
            });
          }
        }
      }

      if (foundQualities.size > 0) {
        return Array.from(foundQualities.values()).sort((a, b) => a.priority - b.priority);
      }

      // Procurar por vídeos diretos no HTML (tags video, source, etc.)
      const directVideoPatterns = [
        /<video[^>]*src="([^"]*\.mp4[^"]*)"/g,
        /<source[^>]*src="([^"]*\.mp4[^"]*)"/g,
        /<source[^>]*src="([^"]*\.webm[^"]*)"/g,
        /src="([^"]*\.mp4[^"]*)"[^>]*type="video/g,
        /src="([^"]*\.webm[^"]*)"[^>]*type="video/g
      ];

      for (const pattern of directVideoPatterns) {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          let videoUrl = match[1];

          if (videoUrl && (videoUrl.startsWith('http') || videoUrl.startsWith('//'))) {
            if (videoUrl.startsWith('//')) {
              videoUrl = 'https:' + videoUrl;
            }

            foundQualities.set('direct', {
              quality: 'Original',
              label: 'Qualidade Original (Direto)',
              size: 'Tamanho variável',
              url: videoUrl,
              format: getFormatFromUrl(videoUrl),
              priority: 1
            });
            break;
          }
        }
      }

      if (foundQualities.size > 0) {
        return Array.from(foundQualities.values()).sort((a, b) => a.priority - b.priority);
      }

      // Se não encontrou múltiplas qualidades, verificar se é um vídeo direto
      const headResponse = await fetch(url, { method: 'HEAD' });
      const contentLength = headResponse.headers.get('content-length');
      const contentType = headResponse.headers.get('content-type');

      if (!contentType || !contentType.includes('video')) {
        throw new Error('Não é um vídeo direto');
      }

      const sizeInMB = contentLength ? (parseInt(contentLength) / 1024 / 1024).toFixed(1) : 'Desconhecido';

      return [
        {
          quality: 'Original',
          label: 'Qualidade Original',
          size: `${sizeInMB} MB`,
          url: url,
          format: getFormatFromUrl(url),
          priority: 1
        }
      ];
    } catch (error) {
      throw new Error('Falha na detecção genérica');
    }
  };

  const getQualityLabel = (quality) => {
    const labels = {
      '2160p': '4K',
      '1440p': 'QHD',
      '1080p': 'Full HD',
      '720p': 'HD',
      '480p': 'SD',
      '360p': 'SD',
      '240p': 'SD',
      '144p': 'SD'
    };

    return labels[quality] || '';
  };

  const getDefaultQualities = (url) => {
    // Tentar detectar o tipo de site para oferecer qualidades mais específicas
    const hostname = new URL(url).hostname.toLowerCase();

    if (hostname.includes('instagram')) {
      return [
        {
          quality: '720p',
          label: '720p HD',
          size: 'Estimado: ~50MB',
          url: url,
          format: 'mp4',
          priority: 1
        },
        {
          quality: '480p',
          label: '480p',
          size: 'Estimado: ~25MB',
          url: url,
          format: 'mp4',
          priority: 2
        }
      ];
    }

    if (hostname.includes('tiktok')) {
      return [
        {
          quality: '720p',
          label: '720p HD',
          size: 'Estimado: ~30MB',
          url: url,
          format: 'mp4',
          priority: 1
        },
        {
          quality: '480p',
          label: '480p',
          size: 'Estimado: ~15MB',
          url: url,
          format: 'mp4',
          priority: 2
        }
      ];
    }

    if (hostname.includes('facebook') || hostname.includes('fb.')) {
      return [
        {
          quality: '1080p',
          label: '1080p Full HD',
          size: 'Estimado: ~100MB',
          url: url,
          format: 'mp4',
          priority: 1
        },
        {
          quality: '720p',
          label: '720p HD',
          size: 'Estimado: ~60MB',
          url: url,
          format: 'mp4',
          priority: 2
        },
        {
          quality: '480p',
          label: '480p',
          size: 'Estimado: ~30MB',
          url: url,
          format: 'mp4',
          priority: 3
        }
      ];
    }

    // Qualidades padrão para sites desconhecidos
    return [
      {
        quality: '1080p',
        label: '1080p Full HD',
        size: 'Estimado: ~120MB',
        url: url,
        format: getFormatFromUrl(url),
        priority: 1
      },
      {
        quality: '720p',
        label: '720p HD',
        size: 'Estimado: ~70MB',
        url: url,
        format: getFormatFromUrl(url),
        priority: 2
      },
      {
        quality: '480p',
        label: '480p',
        size: 'Estimado: ~35MB',
        url: url,
        format: getFormatFromUrl(url),
        priority: 3
      },
      {
        quality: '360p',
        label: '360p',
        size: 'Estimado: ~20MB',
        url: url,
        format: getFormatFromUrl(url),
        priority: 4
      },
      {
        quality: 'Auto',
        label: 'Qualidade Automática',
        size: 'Melhor disponível',
        url: url,
        format: getFormatFromUrl(url),
        priority: 5
      }
    ];
  };

  const getFormatFromUrl = (url) => {
    if (url.includes('.mp4')) return 'mp4';
    if (url.includes('.webm')) return 'webm';
    if (url.includes('.avi')) return 'avi';
    if (url.includes('.mov')) return 'mov';
    return 'mp4'; // padrão
  };

  const handleDownload = () => {
    if (!selectedQuality) {
      Alert.alert('Erro', 'Selecione uma qualidade primeiro');
      return;
    }

    onQualitySelected && onQualitySelected(selectedQuality);
    onDownloadStart && onDownloadStart(selectedQuality);
    onClose();
  };

  const getQualityIcon = (quality) => {
    const q = quality.quality.toLowerCase();

    if (q.includes('2160') || q.includes('4k')) {
      return 'diamond';
    } else if (q.includes('1440') || q.includes('qhd')) {
      return 'star';
    } else if (q.includes('1080') || q.includes('full hd')) {
      return 'videocam';
    } else if (q.includes('720') || q.includes('hd')) {
      return 'videocam-outline';
    } else if (q.includes('480')) {
      return 'tablet-portrait';
    } else if (q.includes('360')) {
      return 'phone-portrait';
    } else if (q.includes('240') || q.includes('144')) {
      return 'phone-portrait-outline';
    } else if (q.includes('auto') || q.includes('original')) {
      return 'settings';
    } else {
      return 'play';
    }
  };

  const getQualityColor = (quality) => {
    const q = quality.quality.toLowerCase();

    if (q.includes('2160') || q.includes('4k')) {
      return '#E91E63'; // Rosa para 4K
    } else if (q.includes('1440') || q.includes('qhd')) {
      return '#9C27B0'; // Roxo para QHD
    } else if (q.includes('1080') || q.includes('full hd')) {
      return '#4CAF50'; // Verde para Full HD
    } else if (q.includes('720') || q.includes('hd')) {
      return '#2196F3'; // Azul para HD
    } else if (q.includes('480')) {
      return '#FF9800'; // Laranja para 480p
    } else if (q.includes('360')) {
      return '#FF5722'; // Vermelho-laranja para 360p
    } else if (q.includes('240') || q.includes('144')) {
      return '#F44336'; // Vermelho para baixa qualidade
    } else if (q.includes('auto') || q.includes('original')) {
      return '#6c5ce7'; // Roxo padrão para auto
    } else {
      return '#607D8B'; // Cinza para desconhecido
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>Selecionar Qualidade</Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color="#fff" />
            </TouchableOpacity>
          </View>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#6c5ce7" />
              <Text style={styles.loadingText}>🎯 Detectando vídeo principal...</Text>
              <Text style={styles.loadingSubtext}>Analisando qualidades disponíveis</Text>
            </View>
          ) : (
            <>
              <ScrollView style={styles.qualitiesList}>
                {qualities.length > 1 && (
                  <View style={styles.mainVideoIndicator}>
                    <Ionicons name="star" size={16} color="#FFD700" />
                    <Text style={styles.mainVideoText}>Vídeo principal detectado</Text>
                  </View>
                )}

                {qualities.map((quality, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.qualityItem,
                      selectedQuality?.quality === quality.quality && styles.selectedQuality,
                      index === 0 && qualities.length > 1 && styles.mainVideoItem
                    ]}
                    onPress={() => setSelectedQuality(quality)}
                  >
                    <View style={styles.qualityIcon}>
                      <Ionicons
                        name={getQualityIcon(quality)}
                        size={24}
                        color={getQualityColor(quality)}
                      />
                      {index === 0 && qualities.length > 1 && (
                        <View style={styles.mainVideoBadge}>
                          <Ionicons name="star" size={12} color="#FFD700" />
                        </View>
                      )}
                    </View>

                    <View style={styles.qualityInfo}>
                      <View style={styles.qualityLabelContainer}>
                        <Text style={styles.qualityLabel}>{quality.label}</Text>
                        {index === 0 && qualities.length > 1 && (
                          <Text style={styles.mainVideoLabel}>PRINCIPAL</Text>
                        )}
                      </View>
                      <Text style={styles.qualityDetails}>
                        {quality.size} • {quality.format.toUpperCase()}
                      </Text>
                    </View>

                    <View style={styles.qualityBadge}>
                      <Text style={[styles.qualityBadgeText, { color: getQualityColor(quality) }]}>
                        {quality.quality}
                      </Text>
                    </View>

                    {selectedQuality?.quality === quality.quality && (
                      <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                    )}
                  </TouchableOpacity>
                ))}
              </ScrollView>

              <View style={styles.footer}>
                <TouchableOpacity 
                  style={styles.cancelButton}
                  onPress={onClose}
                >
                  <Text style={styles.cancelButtonText}>Cancelar</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[styles.downloadButton, !selectedQuality && styles.downloadButtonDisabled]}
                  onPress={handleDownload}
                  disabled={!selectedQuality}
                >
                  <Ionicons name="download" size={20} color="#fff" />
                  <Text style={styles.downloadButtonText}>Baixar</Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    backgroundColor: '#1a1a2e',
    borderRadius: 15,
    width: width * 0.9,
    maxHeight: height * 0.8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
  },
  title: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    color: '#fff',
    marginTop: 15,
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingSubtext: {
    color: '#888',
    marginTop: 5,
    textAlign: 'center',
    fontSize: 14,
  },
  qualitiesList: {
    maxHeight: height * 0.5,
  },
  mainVideoIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    backgroundColor: '#2d2d44',
    marginBottom: 5,
    borderRadius: 8,
    marginHorizontal: 15,
  },
  mainVideoText: {
    color: '#FFD700',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 5,
  },
  qualityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
  },
  selectedQuality: {
    backgroundColor: '#2d2d44',
  },
  mainVideoItem: {
    backgroundColor: '#1a2332',
    borderLeftWidth: 3,
    borderLeftColor: '#FFD700',
  },
  qualityIcon: {
    marginRight: 15,
    position: 'relative',
  },
  mainVideoBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#1a1a2e',
    borderRadius: 8,
    padding: 2,
  },
  qualityInfo: {
    flex: 1,
  },
  qualityLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  qualityLabel: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  mainVideoLabel: {
    color: '#FFD700',
    fontSize: 10,
    fontWeight: 'bold',
    marginLeft: 8,
    backgroundColor: '#2d2d44',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  qualityDetails: {
    color: '#888',
    fontSize: 12,
    marginTop: 2,
  },
  qualityBadge: {
    marginRight: 10,
  },
  qualityBadgeText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  footer: {
    flexDirection: 'row',
    padding: 20,
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#666',
    marginRight: 10,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  downloadButton: {
    flex: 1,
    flexDirection: 'row',
    padding: 15,
    borderRadius: 8,
    backgroundColor: '#6c5ce7',
    marginLeft: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  downloadButtonDisabled: {
    backgroundColor: '#666',
  },
  downloadButtonText: {
    color: '#fff',
    fontSize: 16,
    marginLeft: 5,
  },
});

export default VideoQualitySelector;
